
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

const SignupPage: React.FC = () => {
  const { signUpUser } = useAuth();
  const navigate = useNavigate();
  const [username, setUsername] = useState('');
  const [avatarSeed, setAvatarSeed] = useState('');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setMessage('');
    if (!username.trim()) {
      setError("Username is required.");
      return;
    }
    try {
      const newUser = await signUpUser(username.trim(), avatarSeed.trim() || username.trim());
      if (newUser) {
        setMessage(`Account '${newUser.username}' created successfully. It is now pending admin approval.`);
        setUsername('');
        setAvatarSeed('');
        // Optionally navigate away or clear form after a delay
        // setTimeout(() => navigate('/'), 3000);
      } else {
        // Error should be handled by alert in signUpUser for taken username
        // For other potential errors, if any:
        setError("Could not create account. Username might be taken or an error occurred.");
      }
    } catch (err) {
      setError("An unexpected error occurred during sign up.");
      console.error(err);
    }
  };

  return (
    <div className="max-w-md mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <h1 className="text-3xl font-bold text-brand-primary mb-8 text-center animate-text-glow hover-text-glow">Sign Up for S3Kt0R-Gram</h1>
      {message && <p className="mb-4 text-center text-accent-success bg-green-500/10 p-3 rounded-md animate-scale-in hover-glow">{message}</p>}
      {error && <p className="mb-4 text-center text-accent-error bg-red-500/10 p-3 rounded-md animate-scale-in animate-cyber-flicker">{error}</p>}
      <form onSubmit={handleSubmit} className="space-y-6 bg-neutral-surface p-6 sm:p-8 rounded-lg shadow-2xl border border-neutral-border animate-slide-up hover-glow cyber-border">
        <div>
          <label htmlFor="username" className="block text-sm font-medium text-neutral-300 mb-1">
            Username
          </label>
          <input
            type="text"
            id="username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder="Choose your anonymous handle"
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary transition-all duration-200 hover:border-brand-primary/50 hover-glow"
            required
          />
        </div>
        <div>
          <label htmlFor="avatarSeed" className="block text-sm font-medium text-neutral-300 mb-1">
            Avatar Seed (Optional)
          </label>
          <input
            type="text"
            id="avatarSeed"
            value={avatarSeed}
            onChange={(e) => setAvatarSeed(e.target.value)}
            placeholder="Text to generate unique avatar (e.g., your_nickname)"
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary transition-all duration-200 hover:border-brand-primary/50 hover-glow"
          />
           <p className="mt-1 text-xs text-neutral-muted">If blank, username will be used to seed the avatar from picsum.photos.</p>
        </div>
        <button
          type="submit"
          className="w-full bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-2.5 px-4 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-surface focus:ring-brand-secondary transform hover:scale-105 hover:shadow-lg hover:shadow-brand-primary/25 hover-glow"
        >
          Sign Up
        </button>
      </form>
       <p className="mt-6 text-center text-sm text-neutral-muted">
        Already have an account or been approved?{' '}
        <button onClick={() => navigate('/')} className="font-medium text-brand-primary hover:text-brand-secondary transition-all duration-200 hover-text-glow hover-scale">
          Login from Navbar
        </button>
      </p>
    </div>
  );
};

export default SignupPage;

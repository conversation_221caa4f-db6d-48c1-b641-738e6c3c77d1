
import React from 'react';
import PostCard from '../components/PostCard';
import { usePosts } from '../hooks/usePosts';

const HomePage: React.FC = () => {
  const { posts } = usePosts();

  return (
    <div className="max-w-4xl mx-auto py-4 px-2 sm:px-4 lg:px-6">
      {posts.length === 0 && (
        <div className="text-center text-neutral-muted py-10 animate-slide-up">
          <p className="text-xl animate-text-glow">No posts yet.</p>
          <p className="hover-text-glow transition-all duration-300">Be the first to share something revolutionary!</p>
        </div>
      )}

      {/* Compact posts grid for better space utilization */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 lg:gap-4">
        {posts.map((post, index) => (
          <div key={post.id} style={{ animationDelay: `${index * 0.05}s` }}>
            <PostCard post={post} isCompact={true} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default HomePage;

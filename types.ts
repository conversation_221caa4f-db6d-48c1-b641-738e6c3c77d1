export interface User {
  id: string;
  username: string;
  avatarUrl: string;
  isActive: boolean;
  isPendingApproval: boolean;
  bio?: string; // User bio (up to 5 lines)
}

export interface Comment {
  id: string;
  userId: string;
  username: string;
  avatarUrl: string;
  text: string;
  timestamp: string; // ISO string
}

export interface Post {
  id: string;
  userId: string;
  username: string;
  userAvatarUrl: string;
  imageUrl: string;
  caption: string;
  contentBody?: string; // Added: Main content of the post
  likes: number;
  isLikedByCurrentUser?: boolean; // For UI state
  comments: Comment[];
  timestamp: string; // ISO string
  tags?: string[];
  isAnonymous?: boolean; // For anonymous posts
  actualUserId?: string; // Real user ID for anonymous posts (admin only)
}

export const ADMIN_USER_ID = 'admin_s3kt0r_truth';

export interface LoginScreenConfig {
  message: string;
  imageUrl: string;
  imageOverlayOpacity?: number; // 0 to 1
}

export interface AdminMessage {
  id: string;
  senderName: string;
  senderEmail?: string;
  subject: string;
  message: string;
  timestamp: string;
  isRead: boolean;
}

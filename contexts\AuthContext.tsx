
import React, { createContext, useState, ReactNode, useMemo, useCallback, useEffect } from 'react';
import { User, LoginScreenConfig, AdminMessage } from '../types';
import { DEFAULT_LOGIN_SCREEN_CONFIG, LOGIN_SCREEN_CONFIG_KEY } from '../constants';
import { v4 as uuidv4 } from 'uuid';
import { onAuthStateChanged, User as FirebaseUser } from 'firebase/auth';
import { auth } from '../firebase';
import { getUserById, signIn, signUp as firebaseSignUp, logOut, getAllUsers } from '../services/firebaseService';

interface AuthContextType {
  currentUser: User | null;
  users: User[];
  isAdmin: boolean;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  signUpUser: (email: string, password: string, username: string) => Promise<User | null>;
  approveUser: (userId: string) => void;
  toggleUserActivation: (userId: string) => void;
  removeUser: (userId: string) => void;
  loginScreenConfig: LoginScreenConfig;
  updateLoginScreenConfig: (newConfig: Partial<LoginScreenConfig>) => void;
  adminMessages: AdminMessage[];
  sendMessageToAdmin: (messageData: Omit<AdminMessage, 'id' | 'timestamp' | 'isRead'>) => Promise<void>;
  markMessageAsRead: (messageId: string) => void;
  deleteAdminMessage: (messageId: string) => void;
  updateUserProfile: (userId: string, updates: { bio?: string; avatarUrl?: string }) => void;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [users, setUsers] = useState<User[]>([]);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const [loginScreenConfig, setLoginScreenConfig] = useState<LoginScreenConfig>(() => {
    try {
      const storedConfig = localStorage.getItem(LOGIN_SCREEN_CONFIG_KEY);
      return storedConfig ? JSON.parse(storedConfig) : DEFAULT_LOGIN_SCREEN_CONFIG;
    } catch (error) {
      console.error("Error loading login screen config from localStorage:", error);
      return DEFAULT_LOGIN_SCREEN_CONFIG;
    }
  });

  const [adminMessages, setAdminMessages] = useState<AdminMessage[]>([]);

  const isAdmin = useMemo(() => currentUser?.isAdmin === true, [currentUser]);

  // Firebase auth state listener
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
      if (firebaseUser) {
        try {
          const userData = await getUserById(firebaseUser.uid);
          setCurrentUser(userData);
        } catch (error) {
          console.error('Error fetching user data:', error);
          setCurrentUser(null);
        }
      } else {
        setCurrentUser(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Load users for admin
  useEffect(() => {
    if (isAdmin) {
      const loadUsers = async () => {
        try {
          const allUsers = await getAllUsers();
          setUsers(allUsers);
        } catch (error) {
          console.error('Error loading users:', error);
        }
      };
      loadUsers();
    }
  }, [isAdmin]);

  const login = useCallback(async (email: string, password: string): Promise<void> => {
    try {
      await signIn(email, password);
      // The auth state listener will handle setting the current user
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }, []);

  const logout = useCallback(async (): Promise<void> => {
    try {
      await logOut();
      // The auth state listener will handle clearing the current user
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }, []);

  const signUpUser = useCallback(async (email: string, password: string, username: string): Promise<User | null> => {
    try {
      // Check if username is already taken
      if (users.find(u => u.username.toLowerCase() === username.toLowerCase())) {
        throw new Error('Username already taken.');
      }

      const newUser = await firebaseSignUp(email, password, username);

      // Refresh users list for admin
      if (isAdmin) {
        const allUsers = await getAllUsers();
        setUsers(allUsers);
      }

      return newUser;
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  }, [users, isAdmin]);

  const approveUser = useCallback((userId: string) => {
    setUsers(prevUsers =>
      prevUsers.map(user =>
        user.id === userId ? { ...user, isActive: true, isPendingApproval: false } : user
      )
    );
  }, []);

  const toggleUserActivation = useCallback((userId: string) => {
    if (userId === ADMIN_USER_ID) {
        alert("Cannot deactivate the admin account.");
        return;
    }
    setUsers(prevUsers =>
      prevUsers.map(user =>
        user.id === userId ? { ...user, isActive: !user.isActive, isPendingApproval: user.isActive ? false : user.isPendingApproval } : user
      )
    );
    if (currentUser?.id === userId) {
      const user = users.find(u => u.id === userId);
      if(user && !user.isActive) {
           logout();
      }
    }
  }, [currentUser, users, logout]);

  const removeUser = useCallback((userId: string) => {
    if (userId === ADMIN_USER_ID) {
        alert("Cannot remove the admin account.");
        return;
    }
    setUsers(prevUsers => prevUsers.filter(user => user.id !== userId));
    if (currentUser?.id === userId) {
      logout();
    }
  }, [currentUser, logout]);

  const updateLoginScreenConfig = useCallback((newConfig: Partial<LoginScreenConfig>) => {
    setLoginScreenConfig(prevConfig => {
      const updated = { ...prevConfig, ...newConfig };
      try {
        localStorage.setItem(LOGIN_SCREEN_CONFIG_KEY, JSON.stringify(updated));
      } catch (error) {
        console.error("Error saving login screen config to localStorage:", error);
      }
      return updated;
    });
  }, []);

  const sendMessageToAdmin = useCallback(async (messageData: Omit<AdminMessage, 'id' | 'timestamp' | 'isRead'>) => {
    const newMessage: AdminMessage = {
      ...messageData,
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      isRead: false,
    };
    setAdminMessages(prevMessages => [newMessage, ...prevMessages]);
  }, []);

  const markMessageAsRead = useCallback((messageId: string) => {
    setAdminMessages(prevMessages =>
      prevMessages.map(message =>
        message.id === messageId ? { ...message, isRead: true } : message
      )
    );
  }, []);

  const deleteAdminMessage = useCallback((messageId: string) => {
    setAdminMessages(prevMessages => prevMessages.filter(message => message.id !== messageId));
  }, []);

  const updateUserProfile = useCallback((userId: string, updates: { bio?: string; avatarUrl?: string }) => {
    setUsers(prevUsers =>
      prevUsers.map(user =>
        user.id === userId
          ? { ...user, ...updates }
          : user
      )
    );

    // Update current user if it's the same user
    setCurrentUser(prevUser =>
      prevUser && prevUser.id === userId
        ? { ...prevUser, ...updates }
        : prevUser
    );
  }, []);

  const contextValue = useMemo(() => ({
    currentUser,
    users,
    isAdmin,
    loading,
    login,
    logout,
    signUpUser,
    approveUser,
    toggleUserActivation,
    removeUser,
    loginScreenConfig,
    updateLoginScreenConfig,
    adminMessages,
    sendMessageToAdmin,
    markMessageAsRead,
    deleteAdminMessage,
    updateUserProfile,
  }), [currentUser, users, isAdmin, loading, login, logout, signUpUser, approveUser, toggleUserActivation, removeUser, loginScreenConfig, updateLoginScreenConfig, adminMessages, sendMessageToAdmin, markMessageAsRead, deleteAdminMessage, updateUserProfile]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

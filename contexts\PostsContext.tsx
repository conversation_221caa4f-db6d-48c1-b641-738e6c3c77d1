import React, { createContext, useState, ReactNode, useCallback, useMemo } from 'react';
import { Post, Comment, User } from '../types';
import { v4 as uuidv4 } from 'uuid';

type EditablePostData = Partial<Pick<Post, 'imageUrl' | 'caption' | 'contentBody' | 'tags'>>;

interface PostsContextType {
  posts: Post[];
  getPostById: (postId: string) => Post | undefined;
  addPost: (postData: Omit<Post, 'id' | 'likes' | 'comments' | 'timestamp' | 'isLikedByCurrentUser'>) => void;
  editPost: (postId: string, updatedData: EditablePostData) => void;
  deletePost: (postId: string) => void;
  toggleLikePost: (postId: string) => void;
  addCommentToPost: (postId: string, commentText: string, currentUser: User) => void;
  deleteComment: (postId: string, commentId: string) => void;
}

export const PostsContext = createContext<PostsContextType | undefined>(undefined);

interface PostsProviderProps {
  children: ReactNode;
}

export const PostsProvider: React.FC<PostsProviderProps> = ({ children }) => {
  const [posts, setPosts] = useState<Post[]>([]);

  const getPostById = useCallback((postId: string) => {
    return posts.find(post => post.id === postId);
  }, [posts]);

  const addPost = useCallback((postData: Omit<Post, 'id' | 'likes' | 'comments' | 'timestamp' | 'isLikedByCurrentUser'>) => {
    const newPost: Post = {
      ...postData,
      id: uuidv4(),
      likes: 0,
      comments: [],
      timestamp: new Date().toISOString(),
      isLikedByCurrentUser: false,
    };
    setPosts(prevPosts => [newPost, ...prevPosts]);
  }, []);

  const editPost = useCallback((postId: string, updatedData: EditablePostData) => {
    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? { ...post, ...updatedData, timestamp: new Date().toISOString() } // Also update timestamp on edit
          : post
      )
    );
  }, []);

  const deletePost = useCallback((postId: string) => {
    setPosts(prevPosts => prevPosts.filter(post => post.id !== postId));
  }, []);

  const toggleLikePost = useCallback((postId: string) => {
    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? {
              ...post,
              likes: post.isLikedByCurrentUser ? post.likes - 1 : post.likes + 1,
              isLikedByCurrentUser: !post.isLikedByCurrentUser,
            }
          : post
      )
    );
  }, []);

  const addCommentToPost = useCallback((postId: string, commentText: string, currentUser: User) => {
    const newComment: Comment = {
      id: uuidv4(),
      userId: currentUser.id,
      username: currentUser.username,
      avatarUrl: currentUser.avatarUrl,
      text: commentText,
      timestamp: new Date().toISOString(),
    };
    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? { ...post, comments: [newComment, ...post.comments] } // Add new comments to the beginning
          : post
      )
    );
  }, []);

  const deleteComment = useCallback((postId: string, commentId: string) => {
    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? { ...post, comments: post.comments.filter(comment => comment.id !== commentId) }
          : post
      )
    );
  }, []);

  const contextValue = useMemo(() => ({
    posts,
    getPostById,
    addPost,
    editPost,
    deletePost,
    toggleLikePost,
    addCommentToPost,
    deleteComment,
  }), [posts, getPostById, addPost, editPost, deletePost, toggleLikePost, addCommentToPost, deleteComment]);


  return (
    <PostsContext.Provider value={contextValue}>
      {children}
    </PostsContext.Provider>
  );
};